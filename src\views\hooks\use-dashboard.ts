import { ref } from 'vue'
import { taskDashboard } from '@/api/examine'

/**
 * 数据看板相关逻辑
 */
export function useDashboard() {
  // 数据看板数据
  const dashboardData = ref({
    totalCount: 0,        // 审查文件总量
    riskFoundCount: 0,    // 发现风险文件数量
    noRiskCount: 0,       // 未发现风险文件数量
    pendingCount: 0,      // 处理中任务数量
    errorCount: 0         // 审查失败任务数量
  })

  // 获取数据看板数据
  const getDashboardData = async (params: { startTime?: string; endTime?: string } = {}) => {
    const { data, err } = await taskDashboard(params)
    if (err) return

    dashboardData.value = {
      totalCount: data.totalCount || 0,
      riskFoundCount: data.riskFoundCount || 0,
      noRiskCount: data.noRiskCount || 0,
      pendingCount: data.pendingCount || 0,
      errorCount: data.errorCount || 0
    }
  }

  // 重置看板数据
  const resetDashboardData = () => {
    dashboardData.value = {
      totalCount: 0,
      riskFoundCount: 0,
      noRiskCount: 0,
      pendingCount: 0,
      errorCount: 0
    }
  }

  return {
    dashboardData,
    getDashboardData,
    resetDashboardData
  }
}
