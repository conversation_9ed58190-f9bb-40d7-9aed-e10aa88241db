import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { getTaskReview, apiGetFile } from '@/api/examine'

/**
 * 合规审查页面主要逻辑
 */
export function useComplianceReview() {
  // 默认结果数据结构
  const defaultResult = {
    fileId: '',
    finalFileId: '',// 显示文件ID
    fileName: '', // 文件名称
    reviewTime: '', // 审核时间
    dataList: [], // 数据列表
    stats: [], // 统计信息 
    resultFinishNum: 0,
    reviewResult: undefined, 
  }

  // 页面状态
  const state = reactive<Record<string, any>>({
    loading: false,
    activeFilter: null,
    checkListVisible: false,
    historyFilesVisible: false
  })

  // 数据状态
  const statsData = ref<Record<string, any>>({})
  const resultData = reactive<Record<string, any>>({ ...defaultResult })
  const isFirstLoad = ref(true)

  // 筛选标签
  const filterTabs = computed(() => { 
    return [
      { key: null, label: '全部', count: statsData.value.sceneNum},
      { key: 1, label: '发现风险', count: statsData.value.sceneFailureNum},
      { key: 0, label: '未发现风险', count: statsData.value.sceneSuccessNum},
      { key: 2, label: '不适用', count: statsData.value.noUseSceneNum}
    ]
  })

  // 过滤后的项目
  const filteredItems = computed(() => { 
    let categorized = resultData.dataList 
    // 按 reviewItemCode 分类
    categorized = categorized.reduce((acc, item) => {
      let group = acc.find(group => group.reviewItemCode === item.reviewItemCode);
      if (!group) {
        group = {
          reviewItemCode: item.reviewItemCode,
          reviewItemName: item.reviewItemName,
          children: []
        };
        acc.push(group);
      }
      group.children.push(item);
      return acc;
    }, [] as { reviewItemCode: string; reviewItemName: string; children: any[] }[]);  
    return categorized;
  })

  // 获取审查数据
  const getData = async (taskId: string) => { 
    if (!taskId) {
      message.info('缺少任务ID')
      return
    }
    
    state.loading = true 
    Object.assign(resultData, defaultResult)
    
    const { data, err } = await getTaskReview({ 
      taskId, 
      reviewResult: state.activeFilter 
    })
    
    state.loading = false
    Object.assign(resultData, defaultResult, data, {
      resultFinishNum: data.stat?.resultFinishNum || 0
    })
    
    const stats = data?.stats ?? {}
    if (isFirstLoad.value) {
      statsData.value = {
        resultFinishNum: 0,
        ...stats,
        fileName: data.fileName,
        reviewTime: data.reviewTime
      }
    }
    isFirstLoad.value = false
  }

  // 获取文件
  const getFile = async () => {
    if (!resultData.finalFileId) {
      message.info('缺少文件ID')
      return
    }
    
    const { data, err } = await apiGetFile(resultData.finalFileId)
    if (err) return
    
    return data.fileUrl
  }

  // 设置筛选器
  const setActiveFilter = (filterKey: number | null, taskId: string) => {
    state.activeFilter = filterKey 
    getData(taskId)
  }

  // 弹窗控制
  const showCheckList = () => {
    state.checkListVisible = true
  }

  const showHistoryFiles = () => {
    state.historyFilesVisible = true
  }

  // 审查清单保存
  const handleCheckListSave = (checkList: any) => {
    console.log('保存审查清单:', checkList)
    message.success('审查清单已保存')
  }

  // 文件预览处理
  const handleFilePreview = (file: any, callback: () => void) => {
    isFirstLoad.value = true
    callback()
  }

  return {
    state,
    statsData,
    resultData,
    filterTabs,
    filteredItems,
    getData,
    getFile,
    setActiveFilter,
    showCheckList,
    showHistoryFiles,
    handleCheckListSave,
    handleFilePreview
  }
}
