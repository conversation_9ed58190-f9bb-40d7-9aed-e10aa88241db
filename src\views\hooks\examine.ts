import { Clock2,CheckCircle2, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleAlert, CircleX,Loader , CircleCheck } from 'lucide-vue-next'
import { CloseCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
export const statusOptions = [
  {label: '发现风险', value: 11} ,
  {label: '未发现风险', value: 10},
  {label: '待审查', value: 0},
  {label: '审查中', value: 1},
  // {label: '审查完成', value: 2},
  {label: '审查失败', value: -1},
]

/**
 * 风险选项
 */
export const riskOptions = [
  {label: '未发现风险', value: 0, color: 'default'},
  {label: '发现风险', value: 1, color: 'red'}
]
// 审核状态
export const getStatusStyle = (status: number) => {
  const statusConfig = {
    0: { // 待审查
      color: '#000000',
      text: '待审查',
      icon: Clock2

    },
    1: { // 审查中
      color: '#133CE8',
      text: '审查中',
      icon: Refresh<PERSON>w
    },
    2: { // 审查完成
      style: {
        backgroundColor: 'rgba(78, 171, 12, 0.1)',
        borderColor: 'rgba(78, 171, 12, 0.2)',
        color: '#4EAB0C'
      },
      text: '审查完成',
      icon: CheckCircle2
    },
    '-1': { // 审查失败
      color: '#EF4444',
      text: '审查失败',
      icon: CircleX
    }
  }
  return statusConfig[status as keyof typeof statusConfig] || statusConfig[0]
}
//                     
export const getRiskStyle = (status: number) => {
  const statusConfig = {
    0: { // 无风险
      color: '#52C41A',
      text: '未发现风险',
      icon: CircleCheck,
      className: 'safe'
    },
    1: { // 有风险
      color: '#F5222D',
      text: '发现风险',
      icon: CircleAlert,
      className: 'risk'
    },
    2: { // 不试用
      color: '#374151',
      text: '不试用',
      icon: Loader,
      className: 'not-use'
    },
    '-1': { // 审查中
      color: '#133CE8',
      text: '审查中',
      icon: RefreshCw,
      className: 'not-use'
    }
  }
  return statusConfig[status as keyof typeof statusConfig] || {}
}

// 获取文件图标
export const getFileIcon = (fileName: string)=> {
  const extension = fileName.split('.').pop()?.toLowerCase()
  let fileIcon = 'pdf-fill'
  switch (extension) {
    case 'DOCX':
    case 'DOC':
    case 'doc':
    case 'docx':
      fileIcon =  'docx-fill'
      break;
    default:
      break
  }
  return `icon-${fileIcon}`
}
// 获取上传文件状态icon
export const getStatusIcon = (status: string) => { 
  const statusConfig = {
    uploading: {
      icon: RefreshCw,
      text: '上传中'
    },
    done: {
      icon: CheckCircleOutlined,
      text: '上传成功'
    },
    error: {
      icon: CloseCircleOutlined,
      text: '上传失败'
    }
  }
  return statusConfig[status as keyof typeof statusConfig] || statusConfig.done
}
