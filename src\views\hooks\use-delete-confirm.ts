import { ref } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 删除确认相关逻辑
 */
export function useDeleteConfirm() {
  // 删除确认状态
  const dialogOpen = ref(false)
  const batchDeleteMode = ref(false)
  const record2Delete = ref<any>(null)
  const deletePopoverTrigger = ref<HTMLElement>()

  // 单个删除
  const showDeleteConfirm = (record: any, event?: Event) => {
    // 定位Popover到触发按钮的位置
    if (event && deletePopoverTrigger.value) {
      const target = event.currentTarget as HTMLElement
      const rect = target.getBoundingClientRect()
      // 计算按钮中心位置
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      deletePopoverTrigger.value.style.position = 'fixed'
      deletePopoverTrigger.value.style.left = `${centerX + 20}px`
      deletePopoverTrigger.value.style.top = `${centerY}px`
      deletePopoverTrigger.value.style.visibility = 'visible'
    }

    dialogOpen.value = true
    batchDeleteMode.value = false
    record2Delete.value = record
  }

  // 批量删除
  const showBatchDeleteConfirm = (selectedRowKeys: string[], event?: Event) => {
    if (selectedRowKeys.length === 0) {
      message.info('请先选择要删除的项目')
      return
    }

    // 定位Popover到批量删除按钮的位置
    if (event && deletePopoverTrigger.value) {
      const target = event.currentTarget as HTMLElement
      const rect = target.getBoundingClientRect()
      // 计算按钮中心位置
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      deletePopoverTrigger.value.style.position = 'fixed'
      deletePopoverTrigger.value.style.left = `${centerX}px`
      deletePopoverTrigger.value.style.top = `${centerY + 20}px`
      deletePopoverTrigger.value.style.visibility = 'visible'
    }

    batchDeleteMode.value = true
    dialogOpen.value = true
  }

  // 确认删除
  const confirmDelete = async (
    selectedRowKeys: string[], 
    deleteCallback: (taskIds: string | string[]) => Promise<boolean>
  ) => {
    dialogOpen.value = false

    let success = false
    if (batchDeleteMode.value) {
      // 批量删除模式
      success = await deleteCallback(selectedRowKeys)
      batchDeleteMode.value = false
    } else {
      // 单个删除模式
      const { taskId } = record2Delete.value
      success = await deleteCallback(taskId)
    }

    return success
  }

  // 取消删除
  const cancelDelete = () => {
    dialogOpen.value = false
    batchDeleteMode.value = false
    record2Delete.value = null
  }

  return {
    dialogOpen,
    batchDeleteMode,
    record2Delete,
    deletePopoverTrigger,
    showDeleteConfirm,
    showBatchDeleteConfirm,
    confirmDelete,
    cancelDelete
  }
}
