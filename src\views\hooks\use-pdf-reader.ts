import { ref, reactive, computed, nextTick } from 'vue'

/**
 * PDF阅读器相关逻辑
 */
export function usePdfReader() {
  // PDF数据状态
  const pdfData = reactive<Record<string, any>>({
    pdfUrl: '',
    currentPage: 1,
    highlightRects: []
  })

  // 判断是否为PDF文件
  const isPdf = computed(() => {
    if (!pdfData.pdfUrl) return false
    return pdfData.pdfUrl.toLowerCase().endsWith('.pdf')
  })

  // 当前选中的审查项
  const activeItem = ref<Record<string, any>>({})

  // 处理审查项点击
  const handleReviewItemClick = (item: any) => {
    activeItem.value = item ?? {}
    console.log(item, 'active')
    
    nextTick(() => {
      pdfData.currentPage = item.page
      pdfData.highlightRects = item?.position?.map(pos => ({
        page: pos.page,
        x1: pos.x1,
        x2: pos.x2,
        y1: pos.y1,
        y2: pos.y2,
        id: item.uniqueId
      })) ?? []
      console.log(pdfData.highlightRects, 'pdfData.highlightRects')
    })
  }

  // 计算高亮区域
  const getHighlightRects = (dataList: any[]) => {
    return dataList
      .filter(item => item.position && item.result === 1)
      .flatMap(item => 
        item.position.map(pos => ({
          page: pos.page,
          x1: pos.x1,
          x2: pos.x2,
          y1: pos.y1,
          y2: pos.y2,
          text: item.updateStatus === 1 ? item.revisionSuggestion || '' : '',
          id: item.uniqueId
        }))
      )
  }

  // 重置PDF状态
  const resetPdfState = () => {
    pdfData.currentPage = 1
    pdfData.highlightRects = []
    activeItem.value = {}
  }

  // PDF加载完成回调
  const onPdfLoad = () => {
    console.log('PDF加载完成')
  }

  return {
    pdfData,
    isPdf,
    activeItem,
    handleReviewItemClick,
    getHighlightRects,
    resetPdfState,
    onPdfLoad
  }
}
