import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { apiTaskList, apiTaskDelete } from '@/api/examine'
import { DEFAULT_PAGINATION } from './examine'

/**
 * 表格操作相关逻辑
 */
export function useTableOperations() {
  // 表格数据
  const tableData = ref([])
  const loading = ref(false)
  const defaultPageSize = ref(10)

  // 分页配置
  const pagination = ref({
    ...DEFAULT_PAGINATION,
    pageSize: defaultPageSize.value
  })

  // 批量选择
  const selectedRowKeys = ref<string[]>([])
  const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys: string[]) => {
      selectedRowKeys.value = keys
    },
    onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => {
      console.log('onSelectAll', selected, selectedRows, changeRows)
    },
    onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
      console.log('onSelect', record, selected, selectedRows)
    }
  }))

  // 获取表格数据
  const getTableData = async (searchParams: any = {}) => {
    loading.value = true

    const requestParams = {
      pageNum: 1,
      pageSize: pagination.value.pageSize || defaultPageSize.value,
      ...searchParams
    }

    const { data, err } = await apiTaskList(requestParams)
    if (err) {
      message.error('数据加载失败')
      loading.value = false
      return
    }

    const { total, dataList } = data
    pagination.value.current = 1
    pagination.value.total = total
    tableData.value = dataList

    // 清空选择状态，避免选择的项目在新数据中不存在
    selectedRowKeys.value = []
    loading.value = false
  }

  // 表格变化处理
  const onTableChange = async (pag: any, filters: any, sorter: any, extra: any) => {
    loading.value = true
    const { current, pageSize } = pag

    // 更新页面大小
    if (pageSize && pageSize !== defaultPageSize.value) {
      defaultPageSize.value = pageSize
      pagination.value.pageSize = pageSize
      // 页面大小变化时，通常回到第一页
      if (current === 1) {
        pagination.value.current = 1
      }
    }

    // 这里需要从外部传入筛选参数
    const params = {
      pageNum: current,
      pageSize: pageSize || defaultPageSize.value,
      ...extra?.searchParams // 从外部传入的搜索参数
    }

    const { data, err } = await apiTaskList(params)
    if (err) {
      loading.value = false
      return
    }

    const { total, dataList } = data
    pagination.value.current = current
    pagination.value.total = total
    tableData.value = dataList
    loading.value = false
  }

  // 删除操作
  const deleteTask = async (taskIds: string | string[]) => {
    const params = { 
      taskId: Array.isArray(taskIds) ? taskIds.join(',') : taskIds 
    }

    const { err } = await apiTaskDelete(params)
    if (err) return false

    if (Array.isArray(taskIds)) {
      message.success(`成功删除 ${taskIds.length} 个任务`)
      selectedRowKeys.value = []
    } else {
      message.success('删除成功')
    }

    return true
  }

  // 重置表格状态
  const resetTableState = () => {
    selectedRowKeys.value = []
    pagination.value.current = 1
  }

  return {
    tableData,
    loading,
    pagination,
    selectedRowKeys,
    rowSelection,
    getTableData,
    onTableChange,
    deleteTask,
    resetTableState
  }
}
