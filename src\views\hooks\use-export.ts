import { reactive } from 'vue'
import { message } from 'ant-design-vue'
import { apiReviewImport, commentedFile, apiDownload } from '@/api/download'

/**
 * 导出功能相关逻辑
 */
export function useExport() {
  // 导出状态
  const exportState = reactive({
    dropdownVisible: false,
    loading: false,
    options: {
      original: false,
      annotated: false,
      report: false
    }
  })

  // 检查是否有选中的导出选项
  const hasSelectedOptions = () => {
    const options = exportState.options
    return options.original || options.annotated || options.report
  }

  // 取消导出
  const cancelExport = () => {
    exportState.dropdownVisible = false
  }

  // 确认导出
  const confirmExport = async (taskId: string, fileId: string) => {
    try {
      const options = exportState.options
      const params = { taskId }
      const exportPromises = []

      exportState.loading = true

      if (options.original) {
        exportPromises.push(apiDownload(fileId))
      }
      if (options.annotated) {
        exportPromises.push(commentedFile(params))
      }
      if (options.report) {
        exportPromises.push(apiReviewImport(params))
      }

      if (exportPromises.length > 0) {
        await Promise.all(exportPromises)
        exportState.loading = false
        exportState.dropdownVisible = false
        message.success('导出成功')
      }
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
      exportState.loading = false
    }
  }

  // 重置导出选项
  const resetExportOptions = () => {
    exportState.options = {
      original: false,
      annotated: false,
      report: false
    }
  }

  return {
    exportState,
    hasSelectedOptions,
    cancelExport,
    confirmExport,
    resetExportOptions
  }
}
