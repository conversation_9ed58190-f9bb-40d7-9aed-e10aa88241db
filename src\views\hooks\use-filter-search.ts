import { ref, reactive } from 'vue'
import { useDebounceFn } from '@vueuse/core'

/**
 * 筛选搜索相关逻辑
 */
export function useFilterSearch() {
  // 搜索表单
  const form = reactive({
    fileName: '',
    reviewStatus: undefined as number | undefined,
    reviewResult: undefined as number | undefined
  })

  // 日期范围
  const dateRange = ref()

  // 防抖搜索函数
  const createDebouncedSearch = (callback: () => void, delay = 500) => {
    return useDebounceFn(callback, delay)
  }

  // 文件名输入处理
  const onFileNameInput = (debouncedSearch: () => void) => {
    debouncedSearch()
  }

  // 结果变化处理
  const onResultChange = (val: number, callback: () => void) => {
    form.reviewResult = undefined
    if (val === 10) form.reviewResult = 0
    else if (val === 11) form.reviewResult = 1
    callback()
  }

  // 日期范围变化处理
  const onDateRangeChange = (debouncedSearch: () => void) => {
    debouncedSearch()
  }

  // 重置筛选条件
  const resetFilters = (callback: () => void) => {
    Object.assign(form, {
      fileName: '',
      reviewStatus: undefined,
      reviewResult: undefined
    })
    dateRange.value = undefined
    callback()
  }

  // 获取搜索参数
  const getSearchParams = () => {
    let startTime = ''
    let endTime = ''
    if (dateRange.value && dateRange.value.length === 2) {
      startTime = dateRange.value[0].format('YYYY-MM-DD')
      endTime = dateRange.value[1].format('YYYY-MM-DD')
    }

    return {
      fileName: form.fileName,
      reviewStatus: form.reviewStatus === 10 || form.reviewStatus === 11 ? undefined : form.reviewStatus,
      reviewResult: form.reviewResult,
      startTime,
      endTime
    }
  }

  // 获取数据看板搜索参数
  const getDashboardParams = () => {
    let startTime = ''
    let endTime = ''
    if (dateRange.value && dateRange.value.length === 2) {
      startTime = dateRange.value[0].format('YYYY-MM-DD')
      endTime = dateRange.value[1].format('YYYY-MM-DD')
    }

    return { startTime, endTime }
  }

  return {
    form,
    dateRange,
    createDebouncedSearch,
    onFileNameInput,
    onResultChange,
    onDateRangeChange,
    resetFilters,
    getSearchParams,
    getDashboardParams
  }
}
